# 农业订单管理系统 - 前端

## 项目简介

这是一个基于 Vue 3 + TypeScript + Element Plus 的农业订单管理系统前端项目，提供了完整的供应商管理和客户管理功能。

## 技术栈

- **前端框架**: Vue 3
- **开发语言**: TypeScript
- **UI组件库**: Element Plus
- **路由管理**: Vue Router
- **构建工具**: Vite
- **样式**: CSS3 + Element Plus 主题

## 功能特性

### 供应商管理
- ✅ 供应商列表查询（支持分页、搜索、筛选）
- ✅ 供应商信息添加
- ✅ 供应商详情查看
- ✅ 供应商状态管理
- ✅ 响应式设计，支持移动端

### 客户管理
- ✅ 客户列表查询（支持分页、搜索、筛选）
- ✅ 客户信息添加
- ✅ 客户详情查看
- ✅ 客户级别管理
- ✅ 响应式设计，支持移动端

### 系统功能
- ✅ 现代化UI设计
- ✅ 完整的表单验证
- ✅ 错误处理和用户提示
- ✅ 加载状态管理
- ✅ 类型安全的API调用

## 项目结构

```
src/
├── components/          # 通用组件
│   └── QuickNav.vue    # 快速导航组件
├── views/              # 页面组件
│   └── Agriculture/    # 农业相关页面
│       ├── ManagementLayout.vue  # 业务管理布局
│       ├── supplierShow.vue      # 供应商管理页面
│       ├── supplierFan.vue       # 客户管理页面
│       └── components/           # 页面专用组件
│           ├── SupplierForm.vue      # 供应商表单
│           ├── SupplierDetail.vue    # 供应商详情
│           ├── CustomerForm.vue      # 客户表单
│           └── CustomerDetail.vue    # 客户详情
├── services/           # API服务
│   └── api.ts         # API接口定义
├── router/            # 路由配置
│   └── index.ts       # 路由定义
├── assets/            # 静态资源
└── main.ts           # 应用入口
```

## 安装和运行

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 开发环境运行
```bash
npm run dev
```

### 生产环境构建
```bash
npm run build
```

### 代码检查
```bash
npm run lint
```

## 配置说明

### API配置
在项目根目录创建 `.env.local` 文件，配置API基础URL：

```env
VITE_API_BASE_URL=http://localhost:5000
```

### 后端API要求
前端需要后端提供以下API接口：

#### 供应商管理API
- `GET /api/Management/GetSupplier` - 获取供应商列表
- `GET /api/Management/GetSupplierDetail` - 获取供应商详情
- `POST /api/Management/AddSupplier` - 添加供应商

#### 客户管理API
- `GET /api/Management/GetCustomer` - 获取客户列表
- `GET /api/Management/GetCustomerDetail` - 获取客户详情
- `POST /api/Management/AddCustomer` - 添加客户

## 使用说明

### 1. 业务管理
1. 访问首页，点击"业务管理"卡片
2. 进入业务管理界面，包含：
   - 左侧导航菜单（可折叠）
   - 顶部面包屑导航
   - 快速导航组件
   - 主内容区域

### 2. 供应商管理
1. 在业务管理界面，点击左侧"供应商管理"菜单
2. 或使用快速导航组件中的"供应商管理"按钮
3. 在供应商列表页面可以进行以下操作：
   - 搜索供应商（按编号、名称、类型、状态）
   - 添加新供应商（右侧弹出框）
   - 查看供应商详情
   - 编辑供应商信息（待实现）

### 3. 客户管理
1. 在业务管理界面，点击左侧"客户管理"菜单
2. 或使用快速导航组件中的"客户管理"按钮
3. 在客户列表页面可以进行以下操作：
   - 搜索客户（按编号、名称、类型、级别、状态）
   - 添加新客户（右侧弹出框）
   - 查看客户详情
   - 编辑客户信息（待实现）

### 3. 表单验证
所有表单都包含完整的验证规则：
- 必填字段验证
- 手机号格式验证
- 身份证号格式验证
- 统一社会信用代码格式验证
- 字段长度限制

## 开发指南

### 添加新功能
1. 在 `src/views/` 下创建新的页面组件
2. 在 `src/router/index.ts` 中添加路由配置
3. 在 `src/services/api.ts` 中添加API接口定义
4. 在首页添加功能入口

### 组件开发规范
- 使用 TypeScript 进行类型定义
- 使用 Composition API 编写组件
- 遵循 Element Plus 设计规范
- 添加适当的错误处理和加载状态

### API调用规范
- 使用统一的API服务进行调用
- 添加适当的错误处理
- 使用TypeScript类型定义确保类型安全

## 部署说明

### 开发环境
```bash
npm run dev
```

### 生产环境
```bash
npm run build
npm run preview
```

构建后的文件位于 `dist/` 目录，可以部署到任何静态文件服务器。

## 注意事项

1. 确保后端API服务正常运行
2. 检查API基础URL配置是否正确
3. 确保网络连接正常
4. 如遇到CORS问题，需要后端配置跨域支持

## 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 完成供应商管理功能
- ✅ 完成客户管理功能
- ✅ 实现响应式设计
- ✅ 添加完整的表单验证
- ✅ 实现API服务统一管理

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
