import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/home',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
    },
    {
      path: '/',
      name: 'management',
      component: () => import('../views/Agriculture/ManagementLayout.vue'),
      children: [
        {
          path: 'supplier',
          name: 'supplier',
          component: () => import('../views/Agriculture/supplierShow.vue'),
          meta: {
            title: '供应商管理'
          }
        },
        {
          path: 'customer',
          name: 'customer',
          component: () => import('../views/Agriculture/CustomerShow.vue'),
          meta: {
            title: '客户管理'
          }
        }
      ]
    },
  ],
})

export default router
