<template>
  <div class="customer-detail">
    <div v-loading="loading">
      <div v-if="customerData" class="detail-content">
        <!-- 基本信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <label>客户编号：</label>
                <span>{{ customerData.customerNumber }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>客户名称：</label>
                <span>{{ customerData.customerName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>客户类型：</label>
                <span>{{ customerData.customerType }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <label>负责人姓名：</label>
                <span>{{ customerData.personInChargeName || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>负责人手机号：</label>
                <span>{{ customerData.personInChargePhone || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>身份证号：</label>
                <span>{{ customerData.idCard || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <label>所在地：</label>
                <span>{{ customerData.location || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>所属行业：</label>
                <span>{{ customerData.industry || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>使用状态：</label>
                <el-tag :type="customerData.usageStatus === '启用' ? 'success' : 'danger'">
                  {{ customerData.usageStatus }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 客户信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>客户信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <label>客户级别：</label>
                <el-tag :type="getCustomerLevelType(customerData.customerLevel || '')">
                  {{ customerData.customerLevel || '-' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>客户阶段：</label>
                <span>{{ customerData.customerStage || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>客户集团分类：</label>
                <span>{{ customerData.customerGroupClassification || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <label>统一社会信用代码：</label>
                <span>{{ customerData.unifiedSocialCreditCode || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>助记码：</label>
                <span>{{ customerData.mnemonicCode || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <label>录入单位：</label>
                <span>{{ customerData.entryUnit || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>使用单位：</label>
                <span>{{ customerData.usageUnit || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 关联信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>关联信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <label>上级客户：</label>
                <span>{{ customerData.superiorCustomer?.customerName || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>客户自定义标签：</label>
                <span>{{ customerData.customTags || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 位置信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>位置信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <label>地图定位经度：</label>
                <span>{{ customerData.longitude || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>地图定位纬度：</label>
                <span>{{ customerData.latitude || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 其他信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>其他信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <label>归集档案：</label>
                <el-tag :type="customerData.isArchived ? 'warning' : 'info'">
                  {{ customerData.isArchived ? '是' : '否' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>创建时间：</label>
                <span>{{ formatDate(customerData.createTime) }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="detail-item">
                <label>备注：</label>
                <span>{{ customerData.remarks || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 关联信息详情 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>关联信息详情</span>
            </div>
          </template>
          
          <!-- 银行账户 -->
          <div class="section-title">银行账户</div>
          <el-table :data="customerData.bankAccounts || []" stripe>
            <el-table-column prop="bankName" label="银行名称" />
            <el-table-column prop="accountNumber" label="账号" />
            <el-table-column prop="accountHolder" label="开户人" />
            <el-table-column prop="isDefault" label="是否默认">
              <template #default="{ row }">
                <el-tag :type="row.isDefault ? 'success' : 'info'">
                  {{ row.isDefault ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>

          <!-- 联系信息 -->
          <div class="section-title">联系信息</div>
          <el-table :data="customerData.contacts || []" stripe>
            <el-table-column prop="contactName" label="联系人" />
            <el-table-column prop="contactPhone" label="联系电话" />
            <el-table-column prop="contactEmail" label="邮箱" />
            <el-table-column prop="contactType" label="联系类型" />
          </el-table>

          <!-- 服务人员 -->
          <div class="section-title">服务人员</div>
          <el-table :data="customerData.servicePersons || []" stripe>
            <el-table-column prop="servicePersonName" label="服务人员" />
            <el-table-column prop="servicePersonPhone" label="联系电话" />
            <el-table-column prop="serviceType" label="服务类型" />
          </el-table>

          <!-- 产品方案 -->
          <div class="section-title">产品方案</div>
          <el-table :data="customerData.productSolutions || []" stripe>
            <el-table-column prop="productName" label="产品名称" />
            <el-table-column prop="solutionType" label="方案类型" />
            <el-table-column prop="price" label="价格" />
            <el-table-column prop="status" label="状态" />
          </el-table>

          <!-- 折扣方案 -->
          <div class="section-title">折扣方案</div>
          <el-table :data="customerData.discountSolutions || []" stripe>
            <el-table-column prop="discountName" label="折扣名称" />
            <el-table-column prop="discountType" label="折扣类型" />
            <el-table-column prop="discountValue" label="折扣值" />
            <el-table-column prop="status" label="状态" />
          </el-table>
        </el-card>
      </div>

      <div v-else-if="!loading" class="no-data">
        <el-empty description="未找到客户信息" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { customerApi, type Customer } from '@/services/api'

// 定义props
interface Props {
  customerId: number
}

const props = defineProps<Props>()

// 响应式数据
const loading = ref(false)
const customerData = ref<Customer | null>(null)

// 获取客户详情
const getCustomerDetail = async () => {
  loading.value = true
  try {
    const result = await customerApi.getDetail(props.customerId)
    
    if (result.success) {
      customerData.value = result.data
    } else {
      ElMessage.error(result.message || '获取客户详情失败')
    }
  } catch (error) {
    ElMessage.error('网络错误，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 获取客户级别标签类型
const getCustomerLevelType = (level: string) => {
  switch (level) {
    case 'VIP客户':
      return 'danger'
    case '重要客户':
      return 'warning'
    case '普通客户':
      return 'info'
    default:
      return 'info'
  }
}

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleString()
}

// 组件挂载时获取数据
onMounted(() => {
  if (props.customerId) {
    getCustomerDetail()
  }
})
</script>

<style scoped>
.customer-detail {
  padding: 20px 0;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-card {
  border: 1px solid #ebeef5;
}

.card-header {
  font-weight: bold;
  color: #303133;
}

.detail-item {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.detail-item label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
  margin-right: 8px;
}

.detail-item span {
  color: #303133;
  flex: 1;
}

.section-title {
  font-weight: bold;
  color: #303133;
  margin: 20px 0 10px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

:deep(.el-table) {
  margin-bottom: 20px;
}

:deep(.el-card__header) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}
</style> 