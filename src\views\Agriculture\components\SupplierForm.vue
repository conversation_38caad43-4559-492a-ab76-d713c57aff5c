<template>
  <div class="supplier-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="140px"
      @submit.prevent="handleSubmit"
    >
      <!-- 供应商类型 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="供应商类型" prop="supplierType" required>
            <el-radio-group v-model="formData.supplierType">
              <el-radio label="个人">个人</el-radio>
              <el-radio label="企业">企业</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 负责人信息 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="负责人手机号" prop="personInChargePhone" required>
            <el-input v-model="formData.personInChargePhone" placeholder="请输入负责人手机号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人姓名" prop="personInChargeName" required>
            <el-input v-model="formData.personInChargeName" placeholder="请输入负责人姓名" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 供应商名称 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="供应商名称" prop="supplierName" required>
            <el-input v-model="formData.supplierName" placeholder="请输入供应商名称" />
            <div class="form-tip">企业供应商请输入全称，个人供应商建议输入省市县，姓名。</div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 身份证和所在地 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="身份证" prop="idCard">
            <el-input v-model="formData.idCard" placeholder="请填写身份证号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所在地" prop="location">
            <div class="location-input-group">
              <el-select v-model="formData.locationProvince" placeholder="请选择" style="width: 120px">
                <el-option label="北京市" value="北京市" />
                <el-option label="上海市" value="上海市" />
                <el-option label="广东省" value="广东省" />
              </el-select>
              <el-input v-model="formData.locationDetail" placeholder="必须填写四个字以上地址" style="flex: 1; margin-left: 8px;" />
              <el-button type="primary" @click="showMap = true" style="margin-left: 8px;">地图定位</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 地图组件 -->
      <el-row v-if="showMap" :gutter="20">
        <el-col :span="24">
          <el-form-item label="地图定位">
            <div class="map-container">
              <div class="map-placeholder">
                <el-icon size="48" color="#409EFF"><Location /></el-icon>
                <p>地图组件 - 点击选择位置</p>
                <p class="map-copyright">高德地图 © 2023 AutoNavi GS(2021)6375号</p>
              </div>
              <el-button 
                type="text" 
                class="map-close"
                @click="showMap = false"
              >
                <el-icon><Close /></el-icon>
              </el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 行业和统一社会信用代码 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属行业" prop="industry">
            <el-input v-model="formData.industry" placeholder="请输入所属行业" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="统一社会信用代码" prop="unifiedSocialCreditCode">
            <el-input v-model="formData.unifiedSocialCreditCode" placeholder="请填写统一社会信用代码(税号)" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 助记码和供应商属性 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="助记码" prop="mnemonicCode">
            <el-input v-model="formData.mnemonicCode" placeholder="请输入助记码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="供应商属性" prop="supplierAttribute">
            <el-input v-model="formData.supplierAttribute" placeholder="请输入供应商属性" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 所属单位和使用状态 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属单位" prop="affiliatedUnit">
            <el-input v-model="formData.affiliatedUnit" value="宏光养羊" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="使用状态" prop="usageStatus" required>
            <el-radio-group v-model="formData.usageStatus">
              <el-radio :label="true">使用</el-radio>
              <el-radio :label="false">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 引用单位 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="引用单位" prop="referenceUnit">
            <div class="reference-unit-group">
              <el-input v-model="formData.referenceUnit" placeholder="请输入引用单位" />
              <span class="unit-count">共计{{ referenceUnitCount }}家单位</span>
              <el-button type="primary" size="small" @click="addReferenceUnit">+</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 备注 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 服务人员 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属市场" prop="affiliatedMarket">
            <el-select v-model="formData.affiliatedMarket" placeholder="-- 请选择所属市场--" style="width: 100%">
              <el-option label="北京市场" value="北京市场" />
              <el-option label="上海市场" value="上海市场" />
              <el-option label="广州市场" value="广州市场" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="业务员" prop="salesperson">
            <el-select v-model="formData.salesperson" placeholder="---请选择业务员---" style="width: 100%">
              <el-option label="张三" value="张三" />
              <el-option label="李四" value="李四" />
              <el-option label="王五" value="王五" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 表单操作按钮 -->
      <el-form-item class="form-actions">
        <el-button type="primary" @click="handleSubmit" :loading="submitting" size="large">
          <el-icon><Check /></el-icon>
          提交保存
        </el-button>
        <el-button @click="handleReset" size="large">
          <el-icon><Refresh /></el-icon>
          重置表单
        </el-button>
        <el-button @click="handleCancel" size="large">
          <el-icon><Close /></el-icon>
          取消
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Check, Refresh, Close, Location } from '@element-plus/icons-vue'
import { supplierApi, type AddSupplierParams } from '@/services/api'

// 定义事件
const emit = defineEmits<{
  submitSuccess: []
  cancel: []
}>()

// 表单引用
const formRef = ref<FormInstance>()
const submitting = ref(false)
const showMap = ref(false)
const referenceUnitCount = ref(0)

// 表单数据
const formData = reactive({
  supplierNumber: '',
  supplierName: '',
  supplierType: '个人',
  personInChargeName: '',
  personInChargePhone: '',
  idCard: '',
  locationProvince: '',
  locationDetail: '',
  industry: '',
  unifiedSocialCreditCode: '',
  mnemonicCode: '',
  supplierAttribute: '',
  affiliatedUnit: '宏光养羊',
  usageStatus: true,
  referenceUnit: '',
  remarks: '',
  affiliatedMarket: '',
  salesperson: '',
  // 原有字段保持兼容
  location: '',
  entryUnit: '',
  usageUnit: '',
  isArchived: false,
  longitude: null,
  latitude: null
})

// 表单验证规则
const rules: FormRules = {
  supplierName: [
    { required: true, message: '请输入供应商名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  supplierType: [
    { required: true, message: '请选择供应商类型', trigger: 'change' }
  ],
  personInChargeName: [
    { required: true, message: '请输入负责人姓名', trigger: 'blur' }
  ],
  personInChargePhone: [
    { required: true, message: '请输入负责人手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  usageStatus: [
    { required: true, message: '请选择使用状态', trigger: 'change' }
  ],
  idCard: [
    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号', trigger: 'blur' }
  ],
  unifiedSocialCreditCode: [
    { pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/, message: '请输入正确的统一社会信用代码', trigger: 'blur' }
  ]
}

// 添加引用单位
const addReferenceUnit = () => {
  if (formData.referenceUnit.trim()) {
    referenceUnitCount.value++
    formData.referenceUnit = ''
    ElMessage.success('引用单位添加成功')
  } else {
    ElMessage.warning('请先输入引用单位名称')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 合并地址信息
    formData.location = `${formData.locationProvince} ${formData.locationDetail}`.trim()
    
    // 调用API添加供应商
    const result = await supplierApi.add(formData as AddSupplierParams)
    
    if (result.success) {
      ElMessage.success('供应商添加成功')
      emit('submitSuccess')
      handleReset()
    } else {
      ElMessage.error(result.message || '添加失败')
    }
  } catch (error) {
    ElMessage.error('提交失败，请检查表单信息')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    supplierNumber: '',
    supplierName: '',
    supplierType: '个人',
    personInChargeName: '',
    personInChargePhone: '',
    idCard: '',
    locationProvince: '',
    locationDetail: '',
    industry: '',
    unifiedSocialCreditCode: '',
    mnemonicCode: '',
    supplierAttribute: '',
    affiliatedUnit: '宏光养羊',
    usageStatus: true,
    referenceUnit: '',
    remarks: '',
    affiliatedMarket: '',
    salesperson: '',
    location: '',
    entryUnit: '',
    usageUnit: '',
    isArchived: false,
    longitude: null,
    latitude: null
  })
  showMap.value = false
  referenceUnitCount.value = 0
  ElMessage.info('表单已重置')
}

// 取消
const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.supplier-form {
  padding: 20px 0;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-input-number) {
  width: 100%;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.location-input-group {
  display: flex;
  align-items: center;
}

.map-container {
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.map-placeholder {
  height: 300px;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.map-placeholder p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

.map-copyright {
  font-size: 12px !important;
  color: #c0c4cc !important;
  margin-top: 16px !important;
}

.map-close {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reference-unit-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.unit-count {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
}

.form-actions {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  text-align: center;
}

.form-actions .el-button {
  margin: 0 10px;
  min-width: 120px;
}

/* 对话框样式 */
:deep(.supplier-dialog .el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}
</style> 