// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000'

// 通用响应类型
interface ApiResponse<T = any> {
  success: boolean
  message: string
  data: T
}

// 分页响应类型
interface PagedResponse<T> {
  items: T[]
  total: number
  pageIndex: number
  pageSize: number
}

// 供应商相关类型
export interface Supplier {
  id: number
  supplierNumber: string
  supplierName: string
  supplierType: string
  personInChargeName?: string
  personInChargePhone?: string
  idCard?: string
  location?: string
  industry?: string
  unifiedSocialCreditCode?: string
  mnemonicCode?: string
  usageStatus: boolean
  entryUnit?: string
  usageUnit?: string
  remarks?: string
  isArchived: boolean
  longitude?: number
  latitude?: number
  createTime: string
  updateTime: string
  // 关联数据
  bankAccounts?: any[]
  contacts?: any[]
  servicePersons?: any[]
  productPrices?: any[]
  attachments?: any[]
  associatedAccount?: any
}

// 客户相关类型
export interface Customer {
  id: number
  customerNumber: string
  customerName: string
  customerType: string
  personInChargeName?: string
  personInChargePhone?: string
  idCard?: string
  location?: string
  industry?: string
  unifiedSocialCreditCode?: string
  mnemonicCode?: string
  customerStage?: string
  customerLevel?: string
  customerGroupClassification?: string
  usageStatus: string
  entryUnit?: string
  usageUnit?: string
  superiorCustomerId?: number
  superiorCustomer?: Customer
  customTags?: string
  remarks?: string
  isArchived: boolean
  longitude?: number
  latitude?: number
  createTime: string
  updateTime: string
  // 关联数据
  bankAccounts?: any[]
  contacts?: any[]
  servicePersons?: any[]
  productSolutions?: any[]
  discountSolutions?: any[]
  attachments?: any[]
  associatedAccount?: any
}

// 查询参数类型
export interface SupplierQueryParams {
  supplierNumber?: string
  supplierName?: string
  supplierType?: string
  usageStatus?: boolean | null
  pageIndex: number
  pageSize: number
}

export interface CustomerQueryParams {
  customerNumber?: string
  customerName?: string
  customerType?: string
  customerLevel?: string
  usageStatus?: string
  pageIndex: number
  pageSize: number
}

// 添加供应商参数
export interface AddSupplierParams {
  supplierNumber: string
  supplierName: string
  supplierType: string
  personInChargeName?: string
  personInChargePhone?: string
  idCard?: string
  location?: string
  industry?: string
  unifiedSocialCreditCode?: string
  mnemonicCode?: string
  usageStatus: boolean
  entryUnit?: string
  usageUnit?: string
  remarks?: string
  isArchived: boolean
  longitude?: number | null
  latitude?: number | null
}

// 添加客户参数
export interface AddCustomerParams {
  customerNumber: string
  customerName: string
  customerType: string
  personInChargeName?: string
  personInChargePhone?: string
  idCard?: string
  location?: string
  industry?: string
  unifiedSocialCreditCode?: string
  mnemonicCode?: string
  customerStage?: string
  customerLevel: string
  customerGroupClassification?: string
  usageStatus: string
  entryUnit?: string
  usageUnit?: string
  superiorCustomerId?: number | null
  customTags?: string
  remarks?: string
  isArchived: boolean
  longitude?: number | null
  latitude?: number | null
}

// 通用HTTP请求方法
async function request<T>(url: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  }

  const response = await fetch(`${API_BASE_URL}${url}`, {
    ...defaultOptions,
    ...options,
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  return response.json()
}

// 供应商API
export const supplierApi = {
  // 获取供应商列表
  getList: (params: SupplierQueryParams): Promise<ApiResponse<PagedResponse<Supplier>>> => {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value))
      }
    })
    return request(`/api/Management/GetSupplier?${searchParams.toString()}`)
  },

  // 获取供应商详情
  getDetail: (id: number): Promise<ApiResponse<Supplier>> => {
    return request(`/api/Management/GetSupplierDetail?id=${id}`)
  },

  // 添加供应商
  add: (data: AddSupplierParams): Promise<ApiResponse<Supplier>> => {
    return request('/api/Management/AddSupplier', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  },
}

// 客户API
export const customerApi = {
  // 获取客户列表
  getList: (params: CustomerQueryParams): Promise<ApiResponse<PagedResponse<Customer>>> => {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value))
      }
    })
    return request(`/api/Management/GetCustomer?${searchParams.toString()}`)
  },

  // 获取客户详情
  getDetail: (id: number): Promise<ApiResponse<Customer>> => {
    return request(`/api/Management/GetCustomerDetail?id=${id}`)
  },

  // 添加客户
  add: (data: AddCustomerParams): Promise<ApiResponse<Customer>> => {
    return request('/api/Management/AddCustomer', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  },
}

// 导出默认API对象
export default {
  supplier: supplierApi,
  customer: customerApi,
} 